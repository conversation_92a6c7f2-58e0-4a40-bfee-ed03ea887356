<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>System Alert - Critical Notification</title>
    <!--[if mso]>
      <noscript>
        <xml>
          <o:OfficeDocumentSettings>
            <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
        </xml>
      </noscript>
    <![endif]-->
    <style>
      @import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      /* Enhanced animations */
      .pulse {
        animation: pulse 2s infinite;
      }

      @keyframes pulse {
        0% {
          transform: scale(1);
          opacity: 1;
        }
        50% {
          transform: scale(1.05);
          opacity: 0.8;
        }
        100% {
          transform: scale(1);
          opacity: 1;
        }
      }

      .glow {
        animation: glow 3s ease-in-out infinite alternate;
      }

      @keyframes glow {
        from {
          box-shadow: 0 0 20px rgba(255, 107, 107, 0.4);
        }
        to {
          box-shadow: 0 0 30px rgba(255, 107, 107, 0.8),
            0 0 40px rgba(255, 107, 107, 0.6);
        }
      }

      .gradient-bg {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      .alert-badge {
        display: inline-block;
        background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        color: white;
        padding: 10px 20px;
        border-radius: 25px;
        font-size: 13px;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 1.2px;
        box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        border: 2px solid rgba(255, 255, 255, 0.3);
      }

      .status-indicator {
        display: inline-block;
        width: 14px;
        height: 14px;
        border-radius: 50%;
        margin-right: 10px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
      }

      .status-critical {
        background: linear-gradient(45deg, #ff4757, #c44569);
        animation: pulse 2s infinite;
      }
      .status-warning {
        background: linear-gradient(45deg, #ffa502, #ff6348);
      }
      .status-info {
        background: linear-gradient(45deg, #3742fa, #2f3542);
      }

      /* Service status badge styles */
      .service-status[data-status="DOWN"] {
        background: linear-gradient(45deg, #ff4757, #c44569) !important;
      }
      .service-status[data-status="WARNING"] {
        background: linear-gradient(45deg, #ffa502, #ff6348) !important;
      }
      .service-status[data-status="UP"],
      .service-status[data-status="ONLINE"] {
        background: linear-gradient(45deg, #2ed573, #1e90ff) !important;
      }

      /* Enhanced card shadows */
      .card-shadow {
        box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15),
          0 15px 35px rgba(0, 0, 0, 0.1), 0 5px 15px rgba(0, 0, 0, 0.08);
      }

      /* Mobile responsiveness */
      @media only screen and (max-width: 600px) {
        .mobile-padding {
          padding: 20px 15px !important;
        }
        .mobile-text {
          font-size: 14px !important;
        }
        .mobile-title {
          font-size: 24px !important;
        }
      }
    </style>
  </head>
  <body
    style="
      margin: 0;
      padding: 0;
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI',
        Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      background-attachment: fixed;
      min-height: 100vh;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    "
  >
    <!-- Preheader text (hidden but shows in email preview) -->
    <div
      style="
        display: none;
        max-height: 0;
        overflow: hidden;
        font-size: 1px;
        line-height: 1px;
        color: transparent;
      "
    >
      🚨 CRITICAL SYSTEM ALERT - Immediate attention required for system issues
    </div>

    <!-- Main Container -->
    <table
      width="100%"
      cellpadding="0"
      cellspacing="0"
      style="
        padding: 40px 20px;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      "
      role="presentation"
    >
      <tr>
        <td align="center">
          <!-- Email Card -->
          <table
            width="650"
            cellpadding="0"
            cellspacing="0"
            class="card-shadow glow"
            style="
              background: #ffffff;
              border-radius: 20px;
              overflow: hidden;
              box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15),
                0 15px 35px rgba(0, 0, 0, 0.1), 0 5px 15px rgba(0, 0, 0, 0.08);
              border: 1px solid rgba(255, 255, 255, 0.3);
              max-width: 650px;
              width: 100%;
            "
            role="presentation"
          >
            <!-- Alert Header -->
            <tr>
              <td
                style="
                  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
                  padding: 0;
                  position: relative;
                  overflow: hidden;
                "
              >
                <!-- Decorative background pattern -->
                <div
                  style="
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background-image: radial-gradient(
                        circle at 20% 50%,
                        rgba(255, 255, 255, 0.1) 0%,
                        transparent 50%
                      ),
                      radial-gradient(
                        circle at 80% 20%,
                        rgba(255, 255, 255, 0.1) 0%,
                        transparent 50%
                      ),
                      radial-gradient(
                        circle at 40% 80%,
                        rgba(255, 255, 255, 0.1) 0%,
                        transparent 50%
                      );
                    pointer-events: none;
                  "
                ></div>

                <table
                  width="100%"
                  cellpadding="0"
                  cellspacing="0"
                  role="presentation"
                >
                  <tr>
                    <td
                      class="mobile-padding"
                      style="
                        padding: 40px 30px;
                        color: #ffffff;
                        text-align: center;
                        position: relative;
                        z-index: 1;
                      "
                    >
                      <!-- Company Logo -->
                      <div style="margin-bottom: 25px">
                        <img
                          src="https://i.ibb.co/1tFBQVcc/color-logo.png"
                          alt="Company Logo"
                          style="
                            max-width: 150px;
                            height: auto;
                            border-radius: 10px;
                            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
                            border: 3px solid rgba(255, 255, 255, 0.3);
                            backdrop-filter: blur(10px);
                          "
                        />
                      </div>

                      <!-- Alert Icon with enhanced styling -->
                      <div style="margin-bottom: 20px">
                        <div
                          style="
                            display: inline-block;
                            background: rgba(255, 255, 255, 0.15);
                            border-radius: 50%;
                            padding: 15px;
                            backdrop-filter: blur(10px);
                            border: 2px solid rgba(255, 255, 255, 0.2);
                          "
                        >
                          <span
                            style="
                              font-size: 52px;
                              display: inline-block;
                              filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
                            "
                            class="pulse"
                            >⚠️</span
                          >
                        </div>
                      </div>

                      <!-- Title -->
                      <h1
                        class="mobile-title"
                        style="
                          margin: 0;
                          font-size: 32px;
                          font-weight: 800;
                          letter-spacing: -0.8px;
                          text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
                          line-height: 1.2;
                        "
                      >
                        🚨 CRITICAL SYSTEM ALERT
                      </h1>

                      <!-- Subtitle -->
                      <p
                        class="mobile-text"
                        style="
                          margin: 12px 0 0;
                          font-size: 18px;
                          opacity: 0.95;
                          font-weight: 500;
                          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                        "
                      >
                        ⏰ Immediate attention required
                      </p>

                      <!-- Enhanced Alert Badge -->
                      <div style="margin-top: 20px">
                        <span class="alert-badge pulse">🔥 URGENT</span>
                      </div>

                      <!-- Timestamp -->
                      <div style="margin-top: 15px">
                        <p
                          style="
                            margin: 0;
                            font-size: 14px;
                            opacity: 0.8;
                            font-weight: 400;
                          "
                        >
                          📅 Alert generated: {{ new Date().toLocaleString() }}
                        </p>
                      </div>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>

            <!-- Alert Content -->
            <tr>
              <td style="padding: 0">
                <!-- Summary Section -->
                <table
                  width="100%"
                  cellpadding="0"
                  cellspacing="0"
                  role="presentation"
                >
                  <tr>
                    <td
                      class="mobile-padding"
                      style="
                        padding: 40px 45px 30px;
                        border-bottom: 2px solid #f8f9fa;
                        background: linear-gradient(
                          135deg,
                          #ffffff 0%,
                          #f8f9fa 100%
                        );
                      "
                    >
                      <table
                        width="100%"
                        cellpadding="0"
                        cellspacing="0"
                        role="presentation"
                      >
                        <tr>
                          <td>
                            <!-- Enhanced section header -->
                            <div
                              style="
                                display: flex;
                                align-items: center;
                                margin-bottom: 20px;
                                padding-bottom: 15px;
                                border-bottom: 3px solid #e3f2fd;
                              "
                            >
                              <div
                                style="
                                  background: linear-gradient(
                                    135deg,
                                    #3498db,
                                    #2980b9
                                  );
                                  width: 50px;
                                  height: 50px;
                                  border-radius: 12px;
                                  display: flex;
                                  align-items: center;
                                  justify-content: center;
                                  margin-right: 15px;
                                  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
                                "
                              >
                                <span style="font-size: 24px">📋</span>
                              </div>
                              <div>
                                <h2
                                  style="
                                    margin: 0;
                                    font-size: 24px;
                                    font-weight: 700;
                                    color: #2c3e50;
                                    line-height: 1.2;
                                  "
                                >
                                  Alert Summary
                                </h2>
                                <p
                                  style="
                                    margin: 4px 0 0;
                                    font-size: 14px;
                                    color: #7f8c8d;
                                    font-weight: 500;
                                  "
                                >
                                  System status overview
                                </p>
                              </div>
                            </div>

                            <!-- Enhanced content box -->
                            <div
                              style="
                                background: linear-gradient(
                                  135deg,
                                  #74b9ff 0%,
                                  #0984e3 100%
                                );
                                padding: 25px;
                                border-radius: 16px;
                                border: 1px solid rgba(255, 255, 255, 0.2);
                                box-shadow: 0 10px 30px rgba(9, 132, 227, 0.2);
                                position: relative;
                                overflow: hidden;
                              "
                            >
                              <!-- Decorative elements -->
                              <div
                                style="
                                  position: absolute;
                                  top: -20px;
                                  right: -20px;
                                  width: 80px;
                                  height: 80px;
                                  background: rgba(255, 255, 255, 0.1);
                                  border-radius: 50%;
                                  pointer-events: none;
                                "
                              ></div>

                              <p
                                class="mobile-text"
                                style="
                                  margin: 0;
                                  color: #ffffff;
                                  font-size: 17px;
                                  line-height: 1.7;
                                  font-weight: 500;
                                  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                                  position: relative;
                                  z-index: 1;
                                "
                              >
                                💬 {{$json["message"]["content"]}}
                              </p>
                            </div>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>

                <!-- Issues Section -->
                <table
                  width="100%"
                  cellpadding="0"
                  cellspacing="0"
                  role="presentation"
                >
                  <tr>
                    <td
                      class="mobile-padding"
                      style="
                        padding: 30px 45px;
                        border-bottom: 2px solid #f8f9fa;
                        background: linear-gradient(
                          135deg,
                          #fff5f5 0%,
                          #ffffff 100%
                        );
                      "
                    >
                      <table
                        width="100%"
                        cellpadding="0"
                        cellspacing="0"
                        role="presentation"
                      >
                        <tr>
                          <td>
                            <!-- Enhanced section header -->
                            <div
                              style="
                                display: flex;
                                align-items: center;
                                margin-bottom: 20px;
                                padding-bottom: 15px;
                                border-bottom: 3px solid #ffebee;
                              "
                            >
                              <div
                                style="
                                  background: linear-gradient(
                                    135deg,
                                    #e74c3c,
                                    #c0392b
                                  );
                                  width: 50px;
                                  height: 50px;
                                  border-radius: 12px;
                                  display: flex;
                                  align-items: center;
                                  justify-content: center;
                                  margin-right: 15px;
                                  box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
                                  animation: pulse 2s infinite;
                                "
                              >
                                <span style="font-size: 24px">🚨</span>
                              </div>
                              <div>
                                <h3
                                  style="
                                    margin: 0;
                                    font-size: 24px;
                                    font-weight: 700;
                                    color: #c0392b;
                                    line-height: 1.2;
                                  "
                                >
                                  Critical Issues
                                </h3>
                                <p
                                  style="
                                    margin: 4px 0 0;
                                    font-size: 14px;
                                    color: #e74c3c;
                                    font-weight: 600;
                                  "
                                >
                                  Requires immediate action
                                </p>
                              </div>
                            </div>

                            <!-- Enhanced issues container -->
                            <div
                              style="
                                background: linear-gradient(
                                  135deg,
                                  #ff7675 0%,
                                  #e17055 100%
                                );
                                padding: 25px;
                                border-radius: 16px;
                                border: 1px solid rgba(255, 255, 255, 0.2);
                                box-shadow: 0 10px 30px rgba(225, 112, 85, 0.2);
                                position: relative;
                                overflow: hidden;
                              "
                            >
                              <!-- Warning pattern background -->
                              <div
                                style="
                                  position: absolute;
                                  top: 0;
                                  left: 0;
                                  right: 0;
                                  bottom: 0;
                                  background-image: repeating-linear-gradient(
                                    45deg,
                                    transparent,
                                    transparent 10px,
                                    rgba(255, 255, 255, 0.05) 10px,
                                    rgba(255, 255, 255, 0.05) 20px
                                  );
                                  pointer-events: none;
                                "
                              ></div>

                              <div style="position: relative; z-index: 1">
                                <div
                                  style="
                                    display: flex;
                                    align-items: center;
                                    margin-bottom: 15px;
                                  "
                                >
                                  <span
                                    class="status-indicator status-critical"
                                  ></span>
                                  <span
                                    style="
                                      color: #ffffff;
                                      font-size: 16px;
                                      font-weight: 700;
                                      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                                    "
                                  >
                                    SYSTEM FAILURES DETECTED
                                  </span>
                                </div>

                                <p
                                  class="mobile-text"
                                  style="
                                    margin: 0;
                                    color: #ffffff;
                                    font-size: 16px;
                                    line-height: 1.7;
                                    font-weight: 500;
                                    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                                  "
                                >
                                  ⚠️ {{
                                  $items("Handle_API_Error")[0].json.issues.join(",
                                  ") }}
                                </p>
                              </div>
                            </div>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>

                <!-- Services Section -->
                <table
                  width="100%"
                  cellpadding="0"
                  cellspacing="0"
                  role="presentation"
                >
                  <tr>
                    <td
                      class="mobile-padding"
                      style="
                        padding: 30px 45px 40px;
                        background: linear-gradient(
                          135deg,
                          #f8f9ff 0%,
                          #ffffff 100%
                        );
                      "
                    >
                      <table
                        width="100%"
                        cellpadding="0"
                        cellspacing="0"
                        role="presentation"
                      >
                        <tr>
                          <td>
                            <!-- Enhanced section header -->
                            <div
                              style="
                                display: flex;
                                align-items: center;
                                margin-bottom: 25px;
                                padding-bottom: 15px;
                                border-bottom: 3px solid #e8eaf6;
                              "
                            >
                              <div
                                style="
                                  background: linear-gradient(
                                    135deg,
                                    #00b894,
                                    #00a085
                                  );
                                  width: 50px;
                                  height: 50px;
                                  border-radius: 12px;
                                  display: flex;
                                  align-items: center;
                                  justify-content: center;
                                  margin-right: 15px;
                                  box-shadow: 0 8px 25px rgba(0, 184, 148, 0.3);
                                "
                              >
                                <span style="font-size: 24px">⚙️</span>
                              </div>
                              <div>
                                <h3
                                  style="
                                    margin: 0;
                                    font-size: 24px;
                                    font-weight: 700;
                                    color: #2c3e50;
                                    line-height: 1.2;
                                  "
                                >
                                  Affected Services
                                </h3>
                                <p
                                  style="
                                    margin: 4px 0 0;
                                    font-size: 14px;
                                    color: #7f8c8d;
                                    font-weight: 500;
                                  "
                                >
                                  Service status breakdown
                                </p>
                              </div>
                            </div>

                            <!-- Enhanced services container -->
                            <div
                              style="
                                background: linear-gradient(
                                  135deg,
                                  #a29bfe 0%,
                                  #6c5ce7 100%
                                );
                                padding: 25px;
                                border-radius: 16px;
                                border: 1px solid rgba(255, 255, 255, 0.2);
                                box-shadow: 0 10px 30px rgba(108, 92, 231, 0.2);
                                position: relative;
                                overflow: hidden;
                              "
                            >
                              <!-- Grid pattern background -->
                              <div
                                style="
                                  position: absolute;
                                  top: 0;
                                  left: 0;
                                  right: 0;
                                  bottom: 0;
                                  background-image: linear-gradient(
                                      rgba(255, 255, 255, 0.05) 1px,
                                      transparent 1px
                                    ),
                                    linear-gradient(
                                      90deg,
                                      rgba(255, 255, 255, 0.05) 1px,
                                      transparent 1px
                                    );
                                  background-size: 20px 20px;
                                  pointer-events: none;
                                "
                              ></div>

                              <div style="position: relative; z-index: 1">
                                <div
                                  style="
                                    color: #ffffff;
                                    font-size: 16px;
                                    line-height: 1.8;
                                  "
                                >
                                  {{
                                  Object.entries($items("Handle_API_Error")[0].json.services)
                                  .map(([key, val]) => `
                                  <div
                                    style="
                                      margin: 12px 0;
                                      padding: 15px 18px;
                                      background: rgba(255, 255, 255, 0.15);
                                      border-radius: 12px;
                                      display: flex;
                                      justify-content: space-between;
                                      align-items: center;
                                      border: 1px solid rgba(255, 255, 255, 0.1);
                                      backdrop-filter: blur(10px);
                                      transition: all 0.3s ease;
                                    "
                                  >
                                    <div
                                      style="display: flex; align-items: center"
                                    >
                                      <span
                                        style="
                                          font-size: 18px;
                                          margin-right: 10px;
                                        "
                                        >🔧</span
                                      >
                                      <span
                                        style="
                                          font-weight: 600;
                                          font-size: 15px;
                                        "
                                        >${key}</span
                                      >
                                    </div>
                                    <span
                                      style="
                                        padding: 8px 16px;
                                        background: linear-gradient(
                                          45deg,
                                          #2ed573,
                                          #1e90ff
                                        );
                                        border-radius: 20px;
                                        font-size: 13px;
                                        font-weight: 700;
                                        text-transform: uppercase;
                                        letter-spacing: 0.5px;
                                        box-shadow: 0 4px 15px
                                          rgba(0, 0, 0, 0.2);
                                      "
                                      class="service-status"
                                      data-status="${val}"
                                      >${val}</span
                                    >
                                  </div>
                                  `).join("") }}
                                </div>
                              </div>
                            </div>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>

            <!-- Action Section -->
            <tr>
              <td
                style="
                  background: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
                  padding: 0;
                  position: relative;
                  overflow: hidden;
                "
              >
                <!-- Decorative background -->
                <div
                  style="
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background-image: radial-gradient(
                        circle at 30% 20%,
                        rgba(255, 255, 255, 0.1) 0%,
                        transparent 50%
                      ),
                      radial-gradient(
                        circle at 70% 80%,
                        rgba(255, 255, 255, 0.1) 0%,
                        transparent 50%
                      );
                    pointer-events: none;
                  "
                ></div>

                <table
                  width="100%"
                  cellpadding="0"
                  cellspacing="0"
                  role="presentation"
                >
                  <tr>
                    <td
                      class="mobile-padding"
                      align="center"
                      style="padding: 35px 40px; position: relative; z-index: 1"
                    >
                      <!-- Enhanced header -->
                      <div style="margin-bottom: 25px">
                        <div
                          style="
                            display: inline-block;
                            background: rgba(255, 255, 255, 0.1);
                            border-radius: 50%;
                            padding: 12px;
                            margin-bottom: 15px;
                            backdrop-filter: blur(10px);
                            border: 2px solid rgba(255, 255, 255, 0.2);
                          "
                        >
                          <span style="font-size: 32px">🔔</span>
                        </div>

                        <h4
                          style="
                            margin: 0 0 10px;
                            color: #ffffff;
                            font-size: 22px;
                            font-weight: 700;
                            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
                          "
                        >
                          Next Steps Required
                        </h4>

                        <p
                          class="mobile-text"
                          style="
                            margin: 0 0 25px;
                            color: rgba(255, 255, 255, 0.9);
                            font-size: 16px;
                            line-height: 1.6;
                            max-width: 400px;
                            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
                          "
                        >
                          Please review the above issues and take immediate
                          action to resolve system problems.
                        </p>
                      </div>

                      <!-- Enhanced action buttons -->
                      <table
                        cellpadding="0"
                        cellspacing="0"
                        role="presentation"
                      >
                        <tr>
                          <td style="padding: 0 10px">
                            <table
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                            >
                              <tr>
                                <td
                                  style="
                                    background: linear-gradient(
                                      45deg,
                                      #00b894,
                                      #00a085
                                    );
                                    padding: 15px 35px;
                                    border-radius: 30px;
                                    border: 2px solid rgba(255, 255, 255, 0.2);
                                    box-shadow: 0 8px 25px
                                      rgba(0, 184, 148, 0.3);
                                    transition: all 0.3s ease;
                                  "
                                >
                                  <a
                                    href="https://crop-pilot-api.azurewebsites.net/health"
                                    style="
                                      color: #ffffff;
                                      text-decoration: none;
                                      font-weight: 700;
                                      font-size: 15px;
                                      letter-spacing: 0.8px;
                                      text-transform: uppercase;
                                      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                                    "
                                  >
                                    🚀 VIEW DASHBOARD
                                  </a>
                                </td>
                              </tr>
                            </table>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>

            <!-- Footer -->
            <tr>
              <td
                style="
                  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                  padding: 25px 40px;
                  border-top: 3px solid #dee2e6;
                "
              >
                <table
                  width="100%"
                  cellpadding="0"
                  cellspacing="0"
                  role="presentation"
                >
                  <tr>
                    <td align="center">
                      <!-- Enhanced footer content with logo -->
                      <div style="margin-bottom: 15px">
                        <img
                          src="https://i.ibb.co/1tFBQVcc/color-logo.png"
                          alt="Company Logo"
                          style="
                            max-width: 120px;
                            height: auto;
                            border-radius: 8px;
                            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                            margin-bottom: 10px;
                          "
                        />
                      </div>

                      <p
                        style="
                          margin: 0 0 12px;
                          font-size: 15px;
                          color: #495057;
                          font-weight: 600;
                          letter-spacing: 0.3px;
                        "
                      >
                        Crop Guard Monitoring System
                      </p>

                      <div
                        style="
                          background: rgba(255, 255, 255, 0.7);
                          padding: 15px 20px;
                          border-radius: 12px;
                          border: 1px solid rgba(0, 0, 0, 0.1);
                          margin-bottom: 15px;
                          backdrop-filter: blur(10px);
                        "
                      >
                        <p
                          style="
                            margin: 0 0 8px;
                            font-size: 13px;
                            color: #6c757d;
                            font-weight: 500;
                          "
                        >
                          📅 Alert generated: {{ new Date().toLocaleString() }}
                        </p>
                        <p
                          style="
                            margin: 0;
                            font-size: 13px;
                            color: #6c757d;
                            font-weight: 500;
                          "
                        >
                          🔄 Auto-refresh: Every 5 minutes
                        </p>
                      </div>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>

          <!-- Enhanced Mobile Responsive Note -->
          <table
            width="650"
            cellpadding="0"
            cellspacing="0"
            style="margin-top: 25px; max-width: 650px; width: 100%"
            role="presentation"
          ></table>
        </td>
      </tr>
    </table>
  </body>
</html>
